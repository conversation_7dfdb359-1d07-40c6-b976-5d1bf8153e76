const express = require('express');
const assessmentIntegrationController = require('../controllers/assessmentIntegrationController');
const { validateBody, validateParams, schemas } = require('../middleware/validation');
const { authenticateToken, setUserContext } = require('../middleware/auth');
const { freeModelLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

/**
 * Assessment Integration Routes
 * Handles assessment-specific chatbot functionality
 */

// Create conversation from assessment
router.post('/conversations/from-assessment',
  authenticateToken,
  setUserContext,
  freeModelLimiter,
  validateBody(schemas.createFromAssessment),
  assessmentIntegrationController.createFromAssessment.bind(assessmentIntegrationController)
);

// Check assessment readiness for chatbot
router.get('/assessment-ready/:userId',
  authenticateToken,
  setUserContext,
  validateParams(schemas.assessmentReady),
  assessmentIntegrationController.checkAssessmentReady.bind(assessmentIntegrationController)
);

// Generate suggested questions for assessment conversation
router.get('/conversations/:conversationId/suggestions',
  authenticateToken,
  setUserContext,
  validateParams(schemas.conversationId),
  assessmentIntegrationController.generateSuggestions.bind(assessmentIntegrationController)
);

// Manual initialization for assessment conversation
router.post('/conversations/auto-initialize',
  authenticateToken,
  setUserContext,
  freeModelLimiter,
  validateBody(schemas.autoInitialize),
  assessmentIntegrationController.autoInitialize.bind(assessmentIntegrationController)
);

module.exports = router;
