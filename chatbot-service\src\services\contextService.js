const axios = require('axios');
const logger = require('../utils/logger');
const { Conversation, Message } = require('../models');

/**
 * Context Service for Assessment Integration
 * Manages conversation context with assessment data
 */
class ContextService {
  constructor() {
    this.archiveServiceUrl = process.env.ARCHIVE_SERVICE_URL;
    this.internalServiceKey = process.env.INTERNAL_SERVICE_KEY;
    this.maxAssessmentContextTokens = parseInt(process.env.MAX_ASSESSMENT_CONTEXT_TOKENS) || 2000;
  }

  /**
   * Build conversation context with assessment data
   */
  async buildConversationContext(conversationId) {
    try {
      const conversation = await Conversation.findByPk(conversationId);
      
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      let systemPrompt = this.getSystemPrompt(conversation.context_type);
      let assessmentContext = '';

      // Add assessment context if available
      if (conversation.context_type === 'assessment' && conversation.context_data) {
        assessmentContext = await this.getAssessmentContext(conversation.context_data);
        if (assessmentContext) {
          systemPrompt += `\n\nUser Assessment Context:\n${assessmentContext}`;
        }
      }

      // Get conversation history
      const messages = await this.getConversationHistory(conversationId);

      return [
        { role: 'system', content: systemPrompt },
        ...messages
      ];
    } catch (error) {
      logger.error('Error building conversation context:', error);
      throw error;
    }
  }

  /**
   * Get system prompt based on context type
   */
  getSystemPrompt(contextType) {
    const prompts = {
      general: `You are a helpful career counseling AI assistant. Provide thoughtful, actionable career guidance based on the user's questions and context.`,
      
      assessment: `You are an expert career advisor AI with access to the user's comprehensive personality and career assessment results.

Your role:
- Provide personalized career guidance based on their RIASEC interests, Big Five personality traits, and VIA Character Strengths
- Explain how their traits translate to career opportunities
- Suggest specific career paths, work environments, and development areas
- Be encouraging and help them understand their unique strengths
- Reference their specific assessment results when relevant

Always be supportive, insightful, and actionable in your responses.`,
      
      career_guidance: `You are a specialized career guidance AI focused on helping users make informed career decisions through detailed analysis and recommendations.`
    };

    return prompts[contextType] || prompts.general;
  }

  /**
   * Get assessment context from conversation data
   */
  async getAssessmentContext(contextData) {
    if (!contextData.assessment_id) {
      return '';
    }

    try {
      // Try to get from analysis_results first (from event)
      if (contextData.analysis_results) {
        return this.summarizeAssessmentData(contextData.analysis_results);
      }

      // Fallback to fetching from archive service
      const assessmentData = await this.fetchAssessmentData(contextData.assessment_id);
      return this.summarizeAssessmentData(assessmentData);
    } catch (error) {
      logger.error('Error fetching assessment context:', error);
      return '';
    }
  }

  /**
   * Summarize assessment data for AI context
   */
  summarizeAssessmentData(assessmentData) {
    try {
      const { riasec, ocean, viaIs, personaProfile } = assessmentData;
      
      if (!riasec && !ocean && !viaIs) {
        return '';
      }

      let summary = '';

      // Get top RIASEC types
      if (riasec) {
        const topRiasec = Object.entries(riasec)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 3)
          .map(([type, score]) => `${type}: ${score}`)
          .join(', ');
        summary += `RIASEC Interests (${topRiasec})`;
      }

      // Get top Big Five traits
      if (ocean) {
        const topOcean = Object.entries(ocean)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 3)
          .map(([trait, score]) => `${trait}: ${score}`)
          .join(', ');
        if (summary) summary += ', ';
        summary += `Big Five Traits (${topOcean})`;
      }

      // Get top VIA strengths
      if (viaIs) {
        const topVia = Object.entries(viaIs)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 3)
          .map(([strength, score]) => `${strength}: ${score}`)
          .join(', ');
        if (summary) summary += ', ';
        summary += `Top Strengths (${topVia})`;
      }

      // Add persona profile summary if available
      if (personaProfile?.summary) {
        summary += `. ${personaProfile.summary}`;
      }

      return summary;
    } catch (error) {
      logger.error('Error summarizing assessment data:', error);
      return '';
    }
  }

  /**
   * Fetch assessment data from archive service
   */
  async fetchAssessmentData(assessmentId) {
    if (!this.archiveServiceUrl) {
      throw new Error('Archive service URL not configured');
    }

    try {
      const response = await axios.get(
        `${this.archiveServiceUrl}/api/archive/assessments/${assessmentId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.internalServiceKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error('Archive service returned error response');
      }
    } catch (error) {
      logger.error('Error fetching assessment data from archive service:', error);
      throw error;
    }
  }

  /**
   * Get conversation history for context
   */
  async getConversationHistory(conversationId, maxMessages = 20) {
    try {
      const messages = await Message.findAll({
        where: { conversation_id: conversationId },
        order: [['created_at', 'ASC']],
        limit: maxMessages
      });

      return messages.map(message => ({
        role: message.sender_type === 'user' ? 'user' : 'assistant',
        content: message.content
      }));
    } catch (error) {
      logger.error('Error fetching conversation history:', error);
      return [];
    }
  }

  /**
   * Get top RIASEC types from assessment data
   */
  getTopRiasecTypes(riasecData, count = 3) {
    if (!riasecData) return [];
    
    return Object.entries(riasecData)
      .sort(([,a], [,b]) => b - a)
      .slice(0, count)
      .map(([type]) => type);
  }

  /**
   * Get top Big Five traits from assessment data
   */
  getTopOceanTraits(oceanData, count = 3) {
    if (!oceanData) return [];
    
    return Object.entries(oceanData)
      .sort(([,a], [,b]) => b - a)
      .slice(0, count)
      .map(([trait]) => trait);
  }

  /**
   * Get top VIA strengths from assessment data
   */
  getTopViaStrengths(viaData, count = 3) {
    if (!viaData) return [];
    
    return Object.entries(viaData)
      .sort(([,a], [,b]) => b - a)
      .slice(0, count)
      .map(([strength]) => strength);
  }

  /**
   * Optimize context for token limits
   */
  optimizeContextForTokens(context, maxTokens) {
    // Simple token estimation (roughly 4 characters per token)
    const estimatedTokens = context.length / 4;
    
    if (estimatedTokens <= maxTokens) {
      return context;
    }

    // Truncate context to fit within token limit
    const maxChars = maxTokens * 4;
    return context.substring(0, maxChars - 100) + '... (context truncated)';
  }

  /**
   * Validate assessment data structure
   */
  validateAssessmentData(assessmentData) {
    if (!assessmentData || typeof assessmentData !== 'object') {
      return false;
    }

    // Check for required assessment components
    const hasRiasec = assessmentData.riasec && typeof assessmentData.riasec === 'object';
    const hasOcean = assessmentData.ocean && typeof assessmentData.ocean === 'object';
    const hasVia = assessmentData.viaIs && typeof assessmentData.viaIs === 'object';

    return hasRiasec || hasOcean || hasVia;
  }
}

module.exports = new ContextService();
