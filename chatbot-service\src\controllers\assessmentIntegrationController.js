const logger = require('../utils/logger');
const assessmentEventHandler = require('../services/assessmentEventHandler');
const contextService = require('../services/contextService');
const { Conversation, Message } = require('../models');
const axios = require('axios');

/**
 * Assessment Integration Controller
 * Handles assessment-specific chatbot endpoints and functionality
 */
class AssessmentIntegrationController {
  /**
   * Create conversation from assessment
   * POST /conversations/from-assessment
   */
  async createFromAssessment(req, res) {
    try {
      const { assessment_id, title, auto_start_message = true } = req.body;
      const userId = req.user.id;

      logger.info('Creating conversation from assessment', {
        userId,
        assessmentId: assessment_id,
        autoStartMessage: auto_start_message
      });

      // Validate assessment exists and belongs to user
      const assessmentData = await this.validateAssessmentAccess(userId, assessment_id);
      
      if (!assessmentData) {
        return res.status(404).json({ 
          success: false,
          error: {
            code: 'ASSESSMENT_NOT_FOUND',
            message: 'Assessment not found or access denied'
          }
        });
      }

      // Check if conversation already exists
      const existingConversation = await assessmentEventHandler.findAssessmentConversation(
        userId, 
        assessment_id
      );

      if (existingConversation) {
        logger.info('Assessment conversation already exists', {
          conversationId: existingConversation.id
        });

        // Get existing welcome message if any
        const welcomeMessage = await Message.findOne({
          where: {
            conversation_id: existingConversation.id,
            sender_type: 'assistant',
            'metadata.type': 'assessment_welcome'
          },
          order: [['created_at', 'ASC']]
        });

        return res.status(200).json({
          success: true,
          data: {
            conversation: existingConversation,
            initial_message: welcomeMessage?.content || null,
            suggestions: welcomeMessage?.metadata?.suggestions || [],
            existing: true
          }
        });
      }

      // Create new conversation with assessment context
      const result = await assessmentEventHandler.createAssessmentConversation(
        userId, 
        assessment_id, 
        assessmentData
      );

      // Get the welcome message if created
      let welcomeMessage = null;
      let suggestions = [];

      if (auto_start_message) {
        const message = await Message.findOne({
          where: {
            conversation_id: result.conversation.id,
            sender_type: 'assistant',
            'metadata.type': 'assessment_welcome'
          }
        });

        if (message) {
          welcomeMessage = message.content;
          suggestions = message.metadata?.suggestions || [];
        }
      }

      res.status(201).json({
        success: true,
        data: {
          conversation: result.conversation,
          initial_message: welcomeMessage,
          suggestions: suggestions,
          existing: false
        }
      });

    } catch (error) {
      logger.error('Error creating conversation from assessment:', error);
      res.status(500).json({ 
        success: false,
        error: {
          code: 'CONVERSATION_CREATION_FAILED',
          message: 'Failed to create assessment conversation'
        }
      });
    }
  }

  /**
   * Check assessment readiness for chatbot
   * GET /assessment-ready/:userId
   */
  async checkAssessmentReady(req, res) {
    try {
      const { userId } = req.params;
      
      // Verify user access (only allow users to check their own status or admin)
      if (req.user.id !== userId && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          error: {
            code: 'ACCESS_DENIED',
            message: 'Access denied'
          }
        });
      }

      logger.info('Checking assessment readiness', { userId });

      // Check if user has completed assessment
      const assessmentStatus = await this.getLatestAssessmentStatus(userId);
      
      if (!assessmentStatus.has_assessment) {
        return res.json({
          success: true,
          data: {
            has_assessment: false,
            ready_for_chatbot: false,
            message: 'No assessment found. Please complete your career assessment first.'
          }
        });
      }

      // Check if conversation already exists
      const existingConversation = await assessmentEventHandler.findAssessmentConversation(
        userId, 
        assessmentStatus.assessment_id
      );

      res.json({
        success: true,
        data: {
          has_assessment: true,
          assessment_date: assessmentStatus.assessment_date,
          assessment_id: assessmentStatus.assessment_id,
          conversation_exists: !!existingConversation,
          conversation_id: existingConversation?.id || null,
          ready_for_chatbot: true
        }
      });

    } catch (error) {
      logger.error('Error checking assessment readiness:', error);
      res.status(500).json({ 
        success: false,
        error: {
          code: 'ASSESSMENT_CHECK_FAILED',
          message: 'Failed to check assessment status'
        }
      });
    }
  }

  /**
   * Generate suggested questions for assessment conversation
   * GET /conversations/:conversationId/suggestions
   */
  async generateSuggestions(req, res) {
    try {
      const { conversationId } = req.params;
      const userId = req.user.id;

      logger.info('Generating suggestions for conversation', {
        conversationId,
        userId
      });

      const conversation = await Conversation.findOne({
        where: { id: conversationId, user_id: userId }
      });

      if (!conversation) {
        return res.status(404).json({ 
          success: false,
          error: {
            code: 'CONVERSATION_NOT_FOUND',
            message: 'Conversation not found'
          }
        });
      }

      if (conversation.context_type !== 'assessment') {
        return res.status(400).json({ 
          success: false,
          error: {
            code: 'INVALID_CONVERSATION_TYPE',
            message: 'This endpoint is only for assessment conversations'
          }
        });
      }

      const suggestions = await assessmentEventHandler.generateAssessmentSuggestions(
        conversation.context_data?.analysis_results
      );

      res.json({ 
        success: true,
        data: {
          suggestions
        }
      });

    } catch (error) {
      logger.error('Error generating suggestions:', error);
      res.status(500).json({ 
        success: false,
        error: {
          code: 'SUGGESTION_GENERATION_FAILED',
          message: 'Failed to generate suggestions'
        }
      });
    }
  }

  /**
   * Manual initialization for assessment conversation
   * POST /conversations/auto-initialize
   */
  async autoInitialize(req, res) {
    try {
      const { user_id, assessment_id } = req.body;
      const requestingUserId = req.user.id;

      // Verify user access (only allow users to initialize their own or admin)
      if (requestingUserId !== user_id && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          error: {
            code: 'ACCESS_DENIED',
            message: 'Access denied'
          }
        });
      }

      logger.info('Manual assessment conversation initialization', {
        userId: user_id,
        assessmentId: assessment_id,
        requestingUserId
      });

      // Validate assessment exists
      const assessmentData = await this.validateAssessmentAccess(user_id, assessment_id);
      
      if (!assessmentData) {
        return res.status(404).json({ 
          success: false,
          error: {
            code: 'ASSESSMENT_NOT_FOUND',
            message: 'Assessment not found or access denied'
          }
        });
      }

      // Create or update conversation
      const result = await assessmentEventHandler.createAssessmentConversation(
        user_id, 
        assessment_id, 
        assessmentData
      );

      res.status(201).json({
        success: true,
        data: {
          conversation: result.conversation,
          created: result.created || false,
          message: 'Assessment conversation initialized successfully'
        }
      });

    } catch (error) {
      logger.error('Error in auto initialization:', error);
      res.status(500).json({ 
        success: false,
        error: {
          code: 'AUTO_INITIALIZATION_FAILED',
          message: 'Failed to initialize assessment conversation'
        }
      });
    }
  }

  /**
   * Validate assessment access for user
   */
  async validateAssessmentAccess(userId, assessmentId) {
    try {
      if (!process.env.ARCHIVE_SERVICE_URL) {
        logger.warn('Archive service URL not configured, skipping assessment validation');
        return { mock: true }; // Return mock data for development
      }

      const response = await axios.get(
        `${process.env.ARCHIVE_SERVICE_URL}/api/archive/assessments/${assessmentId}`,
        {
          headers: {
            'Authorization': `Bearer ${process.env.INTERNAL_SERVICE_KEY}`,
            'X-User-ID': userId
          },
          timeout: 10000
        }
      );

      if (response.data.success) {
        return response.data.data;
      }

      return null;
    } catch (error) {
      logger.error('Error validating assessment access:', error);
      return null;
    }
  }

  /**
   * Get latest assessment status for user
   */
  async getLatestAssessmentStatus(userId) {
    try {
      if (!process.env.ARCHIVE_SERVICE_URL) {
        logger.warn('Archive service URL not configured, returning mock status');
        return {
          has_assessment: false,
          message: 'Archive service not configured'
        };
      }

      const response = await axios.get(
        `${process.env.ARCHIVE_SERVICE_URL}/archive/results`,
        {
          headers: {
            'X-Internal-Service': 'true',
            'X-Service-Key': process.env.INTERNAL_SERVICE_KEY,
            'X-User-ID': userId
          },
          timeout: 10000
        }
      );

      if (response.data.success && response.data.data.results?.length > 0) {
        const latestResult = response.data.data.results[0];
        return {
          has_assessment: true,
          assessment_id: latestResult.assessment_id,
          assessment_date: latestResult.created_at,
          status: latestResult.status
        };
      }

      return {
        has_assessment: false,
        message: 'No completed assessments found'
      };
    } catch (error) {
      logger.error('Error getting assessment status:', error);
      return {
        has_assessment: false,
        message: 'Error checking assessment status'
      };
    }
  }
}

module.exports = new AssessmentIntegrationController();
