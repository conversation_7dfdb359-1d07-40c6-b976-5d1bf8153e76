const amqp = require('amqplib');
const logger = require('../utils/logger');

/**
 * Queue Service for RabbitMQ Integration
 * Handles event consumption for assessment completion events
 */
class QueueService {
  constructor() {
    this.connection = null;
    this.channel = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = parseInt(process.env.RABBITMQ_MAX_RECONNECT_ATTEMPTS) || 10;
    this.reconnectDelay = parseInt(process.env.RABBITMQ_RECONNECT_DELAY) || 5000;
    this.subscribers = new Map();
  }

  /**
   * Initialize RabbitMQ connection
   */
  async initialize() {
    try {
      if (!process.env.RABBITMQ_URL) {
        logger.warn('RabbitMQ URL not configured, skipping queue service initialization');
        return false;
      }

      await this.connect();
      await this.setupExchangeAndQueue();
      
      logger.info('Queue service initialized successfully');
      return true;
    } catch (error) {
      logger.error('Failed to initialize queue service:', error);
      return false;
    }
  }

  /**
   * Connect to RabbitMQ
   */
  async connect() {
    try {
      this.connection = await amqp.connect(process.env.RABBITMQ_URL);
      this.channel = await this.connection.createChannel();
      
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // Handle connection events
      this.connection.on('error', this.handleConnectionError.bind(this));
      this.connection.on('close', this.handleConnectionClose.bind(this));

      logger.info('Connected to RabbitMQ successfully');
    } catch (error) {
      logger.error('Failed to connect to RabbitMQ:', error);
      throw error;
    }
  }

  /**
   * Setup exchange and queue
   */
  async setupExchangeAndQueue() {
    const exchange = process.env.RABBITMQ_EXCHANGE || 'atma_events';
    const queue = process.env.RABBITMQ_QUEUE || 'chatbot_assessment_events';
    const routingKey = process.env.RABBITMQ_ROUTING_KEY || 'analysis_complete';

    try {
      // Declare exchange
      await this.channel.assertExchange(exchange, 'topic', { durable: true });
      
      // Declare queue
      await this.channel.assertQueue(queue, { durable: true });
      
      // Bind queue to exchange
      await this.channel.bindQueue(queue, exchange, routingKey);

      logger.info('Exchange and queue setup completed', {
        exchange,
        queue,
        routingKey
      });
    } catch (error) {
      logger.error('Failed to setup exchange and queue:', error);
      throw error;
    }
  }

  /**
   * Subscribe to events
   */
  async subscribe(eventType, handler) {
    if (!this.isConnected) {
      logger.warn('Cannot subscribe: not connected to RabbitMQ');
      return false;
    }

    try {
      const queue = process.env.RABBITMQ_QUEUE || 'chatbot_assessment_events';
      
      // Store subscriber
      this.subscribers.set(eventType, handler);

      // Start consuming messages
      await this.channel.consume(queue, async (message) => {
        if (message) {
          try {
            const content = JSON.parse(message.content.toString());
            const messageEventType = content.event_type || content.type;

            logger.info('Received queue message', {
              eventType: messageEventType,
              routingKey: message.fields.routingKey
            });

            // Check if we have a handler for this event type
            if (this.subscribers.has(messageEventType)) {
              const handler = this.subscribers.get(messageEventType);
              await handler(content);
              
              // Acknowledge message
              this.channel.ack(message);
              
              logger.info('Message processed successfully', {
                eventType: messageEventType
              });
            } else {
              logger.warn('No handler found for event type', {
                eventType: messageEventType
              });
              
              // Acknowledge message even if no handler (to prevent requeue)
              this.channel.ack(message);
            }
          } catch (error) {
            logger.error('Error processing queue message:', error);
            
            // Reject message and requeue for retry
            this.channel.nack(message, false, true);
          }
        }
      });

      logger.info('Subscribed to events successfully', { eventType });
      return true;
    } catch (error) {
      logger.error('Failed to subscribe to events:', error);
      return false;
    }
  }

  /**
   * Handle connection errors
   */
  handleConnectionError(error) {
    logger.error('RabbitMQ connection error:', error);
    this.isConnected = false;
  }

  /**
   * Handle connection close
   */
  handleConnectionClose() {
    logger.warn('RabbitMQ connection closed');
    this.isConnected = false;
    
    // Attempt to reconnect
    this.attemptReconnect();
  }

  /**
   * Attempt to reconnect
   */
  async attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached, giving up');
      return;
    }

    this.reconnectAttempts++;
    
    logger.info('Attempting to reconnect to RabbitMQ', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.maxReconnectAttempts
    });

    setTimeout(async () => {
      try {
        await this.connect();
        await this.setupExchangeAndQueue();
        
        // Re-subscribe to all events
        for (const [eventType, handler] of this.subscribers) {
          await this.subscribe(eventType, handler);
        }
        
        logger.info('Reconnected to RabbitMQ successfully');
      } catch (error) {
        logger.error('Reconnection failed:', error);
        this.attemptReconnect();
      }
    }, this.reconnectDelay);
  }

  /**
   * Publish event (for testing purposes)
   */
  async publish(eventType, data, routingKey = null) {
    if (!this.isConnected) {
      logger.warn('Cannot publish: not connected to RabbitMQ');
      return false;
    }

    try {
      const exchange = process.env.RABBITMQ_EXCHANGE || 'atma_events';
      const key = routingKey || process.env.RABBITMQ_ROUTING_KEY || 'analysis_complete';
      
      const message = {
        event_type: eventType,
        timestamp: new Date().toISOString(),
        ...data
      };

      await this.channel.publish(
        exchange,
        key,
        Buffer.from(JSON.stringify(message)),
        { persistent: true }
      );

      logger.info('Event published successfully', {
        eventType,
        routingKey: key
      });
      
      return true;
    } catch (error) {
      logger.error('Failed to publish event:', error);
      return false;
    }
  }

  /**
   * Close connection
   */
  async close() {
    try {
      if (this.channel) {
        await this.channel.close();
      }
      if (this.connection) {
        await this.connection.close();
      }
      
      this.isConnected = false;
      logger.info('RabbitMQ connection closed');
    } catch (error) {
      logger.error('Error closing RabbitMQ connection:', error);
    }
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      subscribersCount: this.subscribers.size
    };
  }
}

module.exports = new QueueService();
