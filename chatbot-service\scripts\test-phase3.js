#!/usr/bin/env node

/**
 * Phase 3 Integration Test Script
 * Tests assessment integration components
 */

require('dotenv').config();
const logger = require('../src/utils/logger');

async function testPhase3Components() {
  console.log('🧪 Testing Phase 3 Assessment Integration Components\n');

  try {
    // Test 1: Queue Service
    console.log('1️⃣ Testing Queue Service...');
    const queueService = require('../src/services/queueService');
    const queueStatus = queueService.getStatus();
    console.log('   Queue Service Status:', queueStatus);
    
    // Test 2: Context Service
    console.log('\n2️⃣ Testing Context Service...');
    const contextService = require('../src/services/contextService');
    
    // Test assessment data summarization
    const mockAssessmentData = {
      riasec: { I: 85, R: 70, A: 60, S: 55, E: 45, C: 40 },
      ocean: { O: 80, C: 75, E: 65, A: 70, N: 30 },
      viaIs: { Creativity: 90, Curiosity: 85, Love: 80 }
    };
    
    const summary = contextService.summarizeAssessmentData(mockAssessmentData);
    console.log('   Assessment Summary:', summary);
    
    // Test 3: Assessment Event Handler
    console.log('\n3️⃣ Testing Assessment Event Handler...');
    const assessmentEventHandler = require('../src/services/assessmentEventHandler');
    const handlerStatus = assessmentEventHandler.getStatus();
    console.log('   Event Handler Status:', handlerStatus);
    
    // Test 4: Assessment Integration Controller
    console.log('\n4️⃣ Testing Assessment Integration Controller...');
    const assessmentController = require('../src/controllers/assessmentIntegrationController');
    console.log('   Controller loaded successfully');
    
    // Test 5: Validation Schemas
    console.log('\n5️⃣ Testing Validation Schemas...');
    const { schemas } = require('../src/middleware/validation');
    
    // Test createFromAssessment schema
    const testData = {
      assessment_id: '123e4567-e89b-12d3-a456-426614174000',
      auto_start_message: true
    };
    
    const { error } = schemas.createFromAssessment.validate(testData);
    if (error) {
      console.log('   ❌ Validation failed:', error.details);
    } else {
      console.log('   ✅ Validation schemas working correctly');
    }
    
    console.log('\n✅ All Phase 3 components loaded successfully!');
    console.log('\n📋 Component Summary:');
    console.log('   • Queue Service: Ready for RabbitMQ events');
    console.log('   • Context Service: Assessment data processing ready');
    console.log('   • Event Handler: Assessment completion handling ready');
    console.log('   • API Controller: Assessment integration endpoints ready');
    console.log('   • Validation: Request validation schemas ready');
    
  } catch (error) {
    console.error('❌ Phase 3 component test failed:', error);
    process.exit(1);
  }
}

// Test database models
async function testDatabaseModels() {
  console.log('\n🗄️ Testing Database Models...');
  
  try {
    const { Conversation, Message } = require('../src/models');
    console.log('   ✅ Models loaded successfully');
    
    // Test model associations
    if (Conversation.associations && Message.associations) {
      console.log('   ✅ Model associations configured');
    }
    
  } catch (error) {
    console.error('   ❌ Database model test failed:', error);
  }
}

// Test environment configuration
function testEnvironmentConfig() {
  console.log('\n⚙️ Testing Environment Configuration...');
  
  const requiredEnvVars = [
    'OPENROUTER_API_KEY',
    'DATABASE_URL',
    'JWT_SECRET'
  ];
  
  const phase3EnvVars = [
    'RABBITMQ_URL',
    'ARCHIVE_SERVICE_URL',
    'INTERNAL_SERVICE_KEY'
  ];
  
  console.log('   Core Environment Variables:');
  requiredEnvVars.forEach(envVar => {
    const value = process.env[envVar];
    console.log(`   • ${envVar}: ${value ? '✅ Set' : '❌ Missing'}`);
  });
  
  console.log('\n   Phase 3 Environment Variables:');
  phase3EnvVars.forEach(envVar => {
    const value = process.env[envVar];
    console.log(`   • ${envVar}: ${value ? '✅ Set' : '⚠️ Not set (optional for testing)'}`);
  });
}

// Main test function
async function runTests() {
  console.log('🚀 Phase 3 Assessment Integration - Component Validation\n');
  
  testEnvironmentConfig();
  await testDatabaseModels();
  await testPhase3Components();
  
  console.log('\n🎉 Phase 3 component validation completed successfully!');
  console.log('\n📝 Next Steps:');
  console.log('   1. Start the chatbot service');
  console.log('   2. Run E2E tests with Phase 3 integration');
  console.log('   3. Test in Docker environment');
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
