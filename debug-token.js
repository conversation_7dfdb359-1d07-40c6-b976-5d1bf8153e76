const axios = require('axios');

async function debugTokenIssue() {
  console.log('🔍 Debugging Token Issue...\n');
  
  const baseUrl = 'http://localhost:3000';
  let userToken = null;
  
  try {
    // Step 1: Register user
    console.log('1️⃣ Registering user...');
    const registerResponse = await axios.post(`${baseUrl}/api/auth/register`, {
      email: `debug.user.${Date.now()}@test.com`,
      password: 'TestPassword123!',
      username: `debuguser${Date.now()}`,
      fullName: 'Debug User',
      gender: 'male',
      schoolId: 1
    });
    
    console.log('✅ Registration successful');
    console.log('User ID:', registerResponse.data.data.user.id);
    
    // Step 2: Login
    console.log('\n2️⃣ Logging in...');
    const loginResponse = await axios.post(`${baseUrl}/api/auth/login`, {
      email: registerResponse.data.data.user.email,
      password: 'TestPassword123!'
    });
    
    userToken = loginResponse.data.data.token;
    console.log('✅ Login successful');
    console.log('Token (first 50 chars):', userToken.substring(0, 50) + '...');
    
    // Step 3: Test token with protected endpoint
    console.log('\n3️⃣ Testing token with protected endpoint...');
    const profileResponse = await axios.get(`${baseUrl}/api/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });

    console.log('✅ Token works with protected endpoint');
    console.log('User profile:', profileResponse.data.data.email);
    
    // Step 4: Check token balance
    console.log('\n4️⃣ Checking token balance...');
    const balanceResponse = await axios.get(`${baseUrl}/api/auth/token-balance`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    console.log('✅ Token balance check successful');
    console.log('Token balance:', balanceResponse.data.data.balance);
    
    // Step 5: Try assessment submission
    console.log('\n5️⃣ Attempting assessment submission...');
    const assessmentData = {
      assessmentName: "Debug Test Assessment",
      assessmentTypes: ["riasec", "ocean", "viaIs"],
      responses: {
        riasec: Array.from({length: 48}, (_, i) => ({ questionId: i + 1, answer: Math.floor(Math.random() * 5) + 1 })),
        ocean: Array.from({length: 50}, (_, i) => ({ questionId: i + 1, answer: Math.floor(Math.random() * 5) + 1 })),
        viaIs: Array.from({length: 120}, (_, i) => ({ questionId: i + 1, answer: Math.floor(Math.random() * 5) + 1 }))
      }
    };
    
    const assessmentResponse = await axios.post(`${baseUrl}/api/assessment/submit`, assessmentData, {
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Assessment submission successful');
    console.log('Assessment ID:', assessmentResponse.data.data.assessmentId);
    
  } catch (error) {
    console.error('❌ Error occurred:');
    console.error('Status:', error.response?.status);
    console.error('Status Text:', error.response?.statusText);
    console.error('Error Data:', error.response?.data);
    console.error('Error Message:', error.message);
    
    if (error.response?.status === 401) {
      console.log('\n🔍 Token Details:');
      console.log('Token exists:', !!userToken);
      if (userToken) {
        console.log('Token length:', userToken.length);
        console.log('Token starts with:', userToken.substring(0, 20));
      }
    }
  }
}

debugTokenIssue();
