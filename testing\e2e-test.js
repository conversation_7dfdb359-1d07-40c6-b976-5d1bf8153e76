#!/usr/bin/env node

const axios = require('axios');
const io = require('socket.io-client');
const chalk = require('chalk');
const config = require('./config');
const TestUtils = require('./utils');

class E2ETester {
  constructor() {
    this.testUser = null;
    this.startTime = Date.now();
  }

  async run() {
    console.log(chalk.bold.blue('\n🧪 ATMA E2E Testing Started'));
    console.log(chalk.gray('Testing complete user journey from registration to account deletion\n'));

    try {
      await this.test1_Register();
      await this.test2_Login();
      await this.test3_UpdateProfile();
      await this.test4_SubmitAssessment();
      await this.test5_WaitForCompletion();
      await this.test6_CheckAssessment();

      // Phase 3: Assessment-Chatbot Integration Tests
      await this.test7_CheckChatbotReadiness();
      await this.test8_CreateAssessmentConversation();
      await this.test9_TestAssessmentContext();
      await this.test10_TestSuggestedQuestions();

      await this.test11_DeleteAccount();
      
      this.generateReport();
    } catch (error) {
      TestUtils.logError(`E2E test failed: ${error.message}`);
      console.error(error);
      process.exit(1);
    }
  }

  async test1_Register() {
    TestUtils.logStage('Test 1: User Registration');
    
    const userData = TestUtils.generateRandomUser(1);
    this.testUser = userData;
    
    try {
      const startTime = Date.now();
      const response = await axios.post(`${config.api.baseUrl}/api/auth/register`, {
        email: userData.email,
        password: userData.password
      }, {
        timeout: config.api.timeout
      });

      const duration = Date.now() - startTime;
      
      if (response.data.success) {
        this.testUser.id = response.data.data?.user?.id;
        TestUtils.logSuccess(`User registered successfully in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`Email: ${userData.email}`);
      } else {
        throw new Error('Registration response indicates failure');
      }
      
      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Registration failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async test2_Login() {
    TestUtils.logStage('Test 2: User Login');
    
    try {
      const startTime = Date.now();
      const response = await axios.post(`${config.api.baseUrl}/api/auth/login`, {
        email: this.testUser.email,
        password: this.testUser.password
      }, {
        timeout: config.api.timeout
      });

      const duration = Date.now() - startTime;
      
      if (response.data.success && response.data.data.token) {
        this.testUser.token = response.data.data.token;
        TestUtils.logSuccess(`User logged in successfully in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`Token received: ${this.testUser.token.substring(0, 20)}...`);
      } else {
        throw new Error('Login response missing token');
      }
      
      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Login failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async test3_UpdateProfile() {
    TestUtils.logStage('Test 3: Update User Profile');

    try {
      const startTime = Date.now();

      // First, get available schools to ensure we use a valid school_id
      let schoolId = null;
      try {
        const schoolsResponse = await axios.get(`${config.api.baseUrl}/api/auth/schools`, {
          headers: {
            'Authorization': `Bearer ${this.testUser.token}`
          },
          timeout: config.api.timeout
        });

        if (schoolsResponse.data.success && schoolsResponse.data.data.schools.length > 0) {
          const randomSchool = schoolsResponse.data.data.schools[Math.floor(Math.random() * schoolsResponse.data.data.schools.length)];
          schoolId = randomSchool.id;
        }
      } catch (schoolError) {
        TestUtils.logWarning('Could not fetch schools, proceeding without school_id');
      }

      // Ensure gender is valid (only 'male' or 'female' based on model validation)
      const validGender = ['male', 'female'][Math.floor(Math.random() * 2)];

      const profileData = {
        username: this.testUser.username,
        full_name: this.testUser.full_name,
        date_of_birth: this.testUser.date_of_birth,
        gender: validGender
      };

      // Only add school_id if we found a valid one
      if (schoolId) {
        profileData.school_id = schoolId;
      }

      const response = await axios.put(`${config.api.baseUrl}/api/auth/profile`, profileData, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`,
          'Content-Type': 'application/json'
        },
        timeout: config.api.timeout
      });

      const duration = Date.now() - startTime;

      if (response.data.success) {
        TestUtils.logSuccess(`Profile updated successfully in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`Username: ${this.testUser.username}`);
        TestUtils.logInfo(`Full Name: ${this.testUser.full_name}`);
        TestUtils.logInfo(`Gender: ${validGender}`);
        if (schoolId) {
          TestUtils.logInfo(`School ID: ${schoolId}`);
        }
      } else {
        throw new Error('Profile update response indicates failure');
      }

      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Profile update failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async test4_SubmitAssessment() {
    TestUtils.logStage('Test 4: Submit Assessment');
    
    try {
      const startTime = Date.now();
      const assessmentData = TestUtils.randomizeAssessmentScores(config.assessmentTemplate);
      const idempotencyKey = TestUtils.generateIdempotencyKey();
      
      const response = await axios.post(`${config.api.baseUrl}/api/assessment/submit`, assessmentData, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`,
          'Content-Type': 'application/json',
          'X-Idempotency-Key': idempotencyKey,
          'X-Force-Direct': 'true' // Force direct processing to avoid batch resultId issues
        },
        timeout: config.api.timeout
      });

      const duration = Date.now() - startTime;
      
      if (response.data.success && response.data.data.jobId) {
        this.testUser.jobId = response.data.data.jobId;
        TestUtils.logSuccess(`Assessment submitted successfully in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`Job ID: ${this.testUser.jobId}`);
        TestUtils.logInfo(`Queue Position: ${response.data.data.queuePosition}`);
        TestUtils.logInfo(`Estimated Processing Time: ${response.data.data.estimatedProcessingTime}`);
      } else {
        throw new Error('Assessment submission response missing job ID');
      }
      
      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Assessment submission failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async test5_WaitForCompletion() {
    TestUtils.logStage('Test 5: Wait for Assessment Completion via WebSocket');
    
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      let socket;
      
      const timeout = setTimeout(() => {
        if (socket) socket.disconnect();
        reject(new Error('Assessment completion timeout'));
      }, config.test.assessmentTimeout);

      try {
        socket = io(config.websocket.url, {
          autoConnect: false,
          transports: ['websocket', 'polling'],
          reconnection: true,
          reconnectionAttempts: config.websocket.reconnectionAttempts,
          reconnectionDelay: config.websocket.reconnectionDelay,
          timeout: config.websocket.timeout
        });

        socket.on('connect', () => {
          TestUtils.logInfo('WebSocket connected, authenticating...');
          socket.emit('authenticate', { token: this.testUser.token });
        });

        socket.on('authenticated', (data) => {
          TestUtils.logSuccess(`WebSocket authenticated for user: ${data.email}`);
          TestUtils.logInfo('Waiting for assessment completion...');
        });

        socket.on('auth_error', (data) => {
          clearTimeout(timeout);
          socket.disconnect();
          reject(new Error(`WebSocket authentication failed: ${data.message}`));
        });

        socket.on('analysis-started', (data) => {
          if (data.jobId === this.testUser.jobId) {
            TestUtils.logInfo(`Analysis started: ${data.message}`);
          }
        });

        socket.on('analysis-complete', (data) => {
          if (data.jobId === this.testUser.jobId) {
            clearTimeout(timeout);
            const duration = Date.now() - startTime;
            
            this.testUser.resultId = data.resultId;
            
            TestUtils.logSuccess(`Assessment completed in ${TestUtils.formatDuration(duration)}`);
            TestUtils.logInfo(`Result ID: ${data.resultId}`);
            TestUtils.logInfo(`Message: ${data.message}`);
            
            socket.disconnect();
            resolve(data);
          }
        });

        socket.on('analysis-failed', (data) => {
          if (data.jobId === this.testUser.jobId) {
            clearTimeout(timeout);
            socket.disconnect();
            reject(new Error(`Assessment failed: ${data.error}`));
          }
        });

        socket.on('disconnect', () => {
          TestUtils.logInfo('WebSocket disconnected');
        });

        socket.connect();
        
      } catch (error) {
        clearTimeout(timeout);
        reject(new Error(`WebSocket connection failed: ${error.message}`));
      }
    });
  }

  async test6_CheckAssessment() {
    TestUtils.logStage('Test 6: Check Assessment Results');
    
    try {
      const startTime = Date.now();
      const response = await axios.get(`${config.api.baseUrl}/api/archive/results`, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        },
        timeout: config.api.timeout
      });

      const duration = Date.now() - startTime;
      
      if (response.data.success) {
        TestUtils.logSuccess(`Assessment results retrieved in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`Found ${response.data.data.results?.length || 0} results`);
        
        if (response.data.data.results && response.data.data.results.length > 0) {
          const latestResult = response.data.data.results[0];
          TestUtils.logInfo(`Latest result status: ${latestResult.status}`);
        }
      } else {
        throw new Error('Results retrieval response indicates failure');
      }
      
      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Results check failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async test7_CheckChatbotReadiness() {
    TestUtils.logStage('Test 7: Check Chatbot Readiness for Assessment');

    try {
      const startTime = Date.now();
      const response = await axios.get(`${config.api.baseUrl}/api/chatbot/assessment-ready/${this.testUser.id}`, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        },
        timeout: config.api.timeout
      });

      const duration = Date.now() - startTime;

      if (response.data.success) {
        const data = response.data.data;
        TestUtils.logSuccess(`Chatbot readiness checked in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`Has Assessment: ${data.has_assessment}`);
        TestUtils.logInfo(`Ready for Chatbot: ${data.ready_for_chatbot}`);

        if (data.has_assessment) {
          TestUtils.logInfo(`Assessment ID: ${data.assessment_id}`);
          TestUtils.logInfo(`Assessment Date: ${data.assessment_date}`);
          TestUtils.logInfo(`Conversation Exists: ${data.conversation_exists}`);

          // Store assessment info for next tests
          this.testUser.assessmentId = data.assessment_id;
          this.testUser.conversationExists = data.conversation_exists;
          this.testUser.existingConversationId = data.conversation_id;
        }
      } else {
        throw new Error('Chatbot readiness check response indicates failure');
      }

      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Chatbot readiness check failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async test8_CreateAssessmentConversation() {
    TestUtils.logStage('Test 8: Create Assessment-Based Conversation');

    try {
      const startTime = Date.now();

      // Skip if conversation already exists
      if (this.testUser.conversationExists) {
        TestUtils.logInfo('Assessment conversation already exists, skipping creation');
        this.testUser.conversationId = this.testUser.existingConversationId;
        return;
      }

      const response = await axios.post(`${config.api.baseUrl}/api/chatbot/conversations/from-assessment`, {
        assessment_id: this.testUser.assessmentId,
        auto_start_message: true
      }, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`,
          'Content-Type': 'application/json'
        },
        timeout: config.api.timeout
      });

      const duration = Date.now() - startTime;

      if (response.data.success) {
        const data = response.data.data;
        this.testUser.conversationId = data.conversation.id;

        TestUtils.logSuccess(`Assessment conversation created in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`Conversation ID: ${data.conversation.id}`);
        TestUtils.logInfo(`Conversation Title: ${data.conversation.title}`);
        TestUtils.logInfo(`Context Type: ${data.conversation.context_type}`);
        TestUtils.logInfo(`Has Welcome Message: ${!!data.initial_message}`);
        TestUtils.logInfo(`Suggestions Count: ${data.suggestions?.length || 0}`);

        if (data.initial_message) {
          TestUtils.logInfo(`Welcome Message Preview: ${data.initial_message.substring(0, 100)}...`);
        }

        // Store suggestions for next test
        this.testUser.suggestions = data.suggestions || [];
      } else {
        throw new Error('Assessment conversation creation response indicates failure');
      }

      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Assessment conversation creation failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async test9_TestAssessmentContext() {
    TestUtils.logStage('Test 9: Test Assessment Context in AI Responses');

    try {
      const startTime = Date.now();

      // Send a message that should trigger assessment-aware response
      const testMessage = "Based on my assessment results, what career paths would you recommend for me?";

      const response = await axios.post(`${config.api.baseUrl}/api/chatbot/conversations/${this.testUser.conversationId}/messages`, {
        content: testMessage,
        content_type: 'text'
      }, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000 // Longer timeout for AI response
      });

      const duration = Date.now() - startTime;

      if (response.data.success) {
        const data = response.data.data;

        TestUtils.logSuccess(`Assessment-aware AI response generated in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`User Message ID: ${data.user_message.id}`);
        TestUtils.logInfo(`AI Message ID: ${data.assistant_message.id}`);
        TestUtils.logInfo(`AI Model Used: ${data.assistant_message.metadata?.model || 'Unknown'}`);
        TestUtils.logInfo(`Processing Time: ${data.assistant_message.metadata?.processing_time || 'Unknown'}ms`);
        TestUtils.logInfo(`Response Preview: ${data.assistant_message.content.substring(0, 150)}...`);

        // Check if response seems assessment-aware (contains assessment-related keywords)
        const responseContent = data.assistant_message.content.toLowerCase();
        const assessmentKeywords = ['assessment', 'personality', 'interests', 'strengths', 'riasec', 'big five'];
        const hasAssessmentContext = assessmentKeywords.some(keyword => responseContent.includes(keyword));

        if (hasAssessmentContext) {
          TestUtils.logSuccess('AI response appears to be assessment-aware');
        } else {
          TestUtils.logWarning('AI response may not be using assessment context');
        }
      } else {
        throw new Error('Assessment context test response indicates failure');
      }

      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Assessment context test failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async test10_TestSuggestedQuestions() {
    TestUtils.logStage('Test 10: Test Suggested Questions Generation');

    try {
      const startTime = Date.now();
      const response = await axios.get(`${config.api.baseUrl}/api/chatbot/conversations/${this.testUser.conversationId}/suggestions`, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        },
        timeout: config.api.timeout
      });

      const duration = Date.now() - startTime;

      if (response.data.success) {
        const suggestions = response.data.data.suggestions;

        TestUtils.logSuccess(`Suggested questions generated in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`Suggestions Count: ${suggestions.length}`);

        suggestions.forEach((suggestion, index) => {
          TestUtils.logInfo(`Suggestion ${index + 1}: ${suggestion}`);
        });

        if (suggestions.length > 0) {
          TestUtils.logSuccess('Assessment-based suggestions generated successfully');
        } else {
          TestUtils.logWarning('No suggestions generated');
        }
      } else {
        throw new Error('Suggested questions generation response indicates failure');
      }

      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Suggested questions test failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async test11_DeleteAccount() {
    TestUtils.logStage('Test 7: Delete User Account');

    try {
      const startTime = Date.now();

      // Use the new self-deletion endpoint
      const deletionResults = await TestUtils.deleteUserAccount(this.testUser.token, config.api.baseUrl);

      const duration = Date.now() - startTime;

      if (deletionResults.accountDeleted && deletionResults.errors.length === 0) {
        TestUtils.logSuccess(`Account deleted successfully in ${TestUtils.formatDuration(duration)}`);
        TestUtils.logInfo(`Original email: ${deletionResults.originalEmail}`);
        TestUtils.logInfo(`Deleted at: ${deletionResults.deletedAt}`);
      } else {
        TestUtils.logWarning(`Account deletion failed in ${TestUtils.formatDuration(duration)}`);
        deletionResults.errors.forEach(error => {
          TestUtils.logError(`Deletion error: ${error}`);
        });
        throw new Error(`Account deletion failed: ${deletionResults.errors.join(', ')}`);
      }

      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Account deletion failed: ${error.message}`);
    }
  }

  generateReport() {
    const totalDuration = Date.now() - this.startTime;
    
    console.log(chalk.bold.green('\n📊 E2E TEST REPORT'));
    console.log(chalk.gray('='.repeat(60)));
    
    console.log(chalk.bold(`\n⏱️  Total Test Duration: ${TestUtils.formatDuration(totalDuration)}`));
    console.log(chalk.bold(`👤 Test User: ${this.testUser.email}`));
    
    console.log(chalk.bold.yellow('\n🎯 TEST SUMMARY:'));
    console.log(chalk.green('✅ All E2E tests passed successfully!'));
    console.log(chalk.green('✅ Complete user journey validated'));
    console.log(chalk.green('✅ WebSocket notifications working'));
    console.log(chalk.green('✅ Assessment processing functional'));
    console.log(chalk.green('✅ Phase 3: Assessment-Chatbot integration working'));
    console.log(chalk.green('✅ Personalized AI responses with assessment context'));
    console.log(chalk.green('✅ Suggested questions generation functional'));
    console.log(chalk.green('✅ Assessment-aware conversation creation'));
    
    console.log(chalk.gray('\n' + '='.repeat(60)));
    console.log(chalk.bold.green('✅ E2E test completed successfully!'));
  }
}

// Run the E2E test
if (require.main === module) {
  const e2eTester = new E2ETester();
  e2eTester.run().catch(error => {
    console.error(chalk.red('E2E test failed:'), error);
    process.exit(1);
  });
}

module.exports = E2ETester;
