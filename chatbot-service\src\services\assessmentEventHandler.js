const logger = require('../utils/logger');
const queueService = require('./queueService');
const contextService = require('./contextService');
const openrouterService = require('./openrouterService');
const { Conversation, Message } = require('../models');

/**
 * Assessment Event Handler
 * Handles assessment completion events and creates personalized conversations
 */
class AssessmentEventHandler {
  constructor() {
    this.isInitialized = false;
    this.enableAutoConversation = process.env.ENABLE_AUTO_CONVERSATION_CREATION === 'true';
    this.enablePersonalizedWelcome = process.env.ENABLE_PERSONALIZED_WELCOME_MESSAGES === 'true';
    this.enableSuggestedQuestions = process.env.ENABLE_SUGGESTED_QUESTIONS === 'true';
    this.welcomeMessageMaxTokens = parseInt(process.env.ASSESSMENT_WELCOME_MESSAGE_MAX_TOKENS) || 200;
    this.suggestedQuestionsCount = parseInt(process.env.SUGGESTED_QUESTIONS_COUNT) || 4;
  }

  /**
   * Initialize event handler
   */
  async initialize() {
    try {
      if (!this.enableAutoConversation) {
        logger.info('Auto conversation creation disabled, skipping event handler initialization');
        return false;
      }

      // Initialize queue service
      const queueInitialized = await queueService.initialize();
      if (!queueInitialized) {
        logger.warn('Queue service not initialized, assessment events will not be processed');
        return false;
      }

      // Subscribe to analysis_complete events
      await queueService.subscribe('analysis_complete', this.handleAssessmentComplete.bind(this));

      this.isInitialized = true;
      logger.info('Assessment event handler initialized successfully');
      return true;
    } catch (error) {
      logger.error('Failed to initialize assessment event handler:', error);
      return false;
    }
  }

  /**
   * Handle assessment completion event
   */
  async handleAssessmentComplete(eventData) {
    try {
      const { user_id, assessment_id, analysis_results } = eventData;
      
      logger.info('Processing assessment completion event', {
        userId: user_id,
        assessmentId: assessment_id,
        hasAnalysisResults: !!analysis_results
      });

      // Validate event data
      if (!user_id || !assessment_id) {
        logger.error('Invalid event data: missing user_id or assessment_id');
        return;
      }

      // Check if assessment conversation already exists
      const existingConversation = await this.findAssessmentConversation(user_id, assessment_id);
      
      if (existingConversation) {
        logger.info('Assessment conversation already exists, updating context', {
          conversationId: existingConversation.id
        });
        
        // Update existing conversation with new assessment data
        await this.updateConversationContext(existingConversation.id, analysis_results);
      } else {
        logger.info('Creating new assessment conversation');
        
        // Create new assessment-based conversation
        await this.createAssessmentConversation(user_id, assessment_id, analysis_results);
      }

      logger.info('Assessment completion event processed successfully');

    } catch (error) {
      logger.error('Error handling assessment completion:', error);
      // TODO: Implement retry logic or dead letter queue
    }
  }

  /**
   * Create assessment-based conversation
   */
  async createAssessmentConversation(userId, assessmentId, analysisResults) {
    try {
      // Create conversation with assessment context
      const conversation = await Conversation.create({
        user_id: userId,
        title: 'Career Guidance - Assessment Results',
        context_type: 'assessment',
        context_data: {
          assessment_id: assessmentId,
          analysis_results: analysisResults,
          created_from_event: true
        },
        metadata: {
          auto_generated: true,
          assessment_date: new Date(),
          source: 'analysis_complete_event'
        }
      });

      logger.info('Assessment conversation created', {
        conversationId: conversation.id,
        userId,
        assessmentId
      });

      // Generate personalized welcome message if enabled
      if (this.enablePersonalizedWelcome && analysisResults) {
        const welcomeMessage = await this.generateAssessmentWelcomeMessage(
          conversation.id, 
          analysisResults
        );

        // Generate suggested questions if enabled
        let suggestions = [];
        if (this.enableSuggestedQuestions) {
          suggestions = await this.generateAssessmentSuggestions(analysisResults);
        }

        // Save welcome message
        await Message.create({
          conversation_id: conversation.id,
          sender_type: 'assistant',
          content: welcomeMessage,
          content_type: 'text',
          metadata: {
            type: 'assessment_welcome',
            suggestions: suggestions,
            auto_generated: true
          }
        });

        logger.info('Welcome message created', {
          conversationId: conversation.id,
          suggestionsCount: suggestions.length
        });
      }

      return { conversation, created: true };
    } catch (error) {
      logger.error('Error creating assessment conversation:', error);
      throw error;
    }
  }

  /**
   * Update existing conversation context
   */
  async updateConversationContext(conversationId, analysisResults) {
    try {
      const conversation = await Conversation.findByPk(conversationId);
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      // Update context data with new analysis results
      const updatedContextData = {
        ...conversation.context_data,
        analysis_results: analysisResults,
        updated_at: new Date()
      };

      await conversation.update({
        context_data: updatedContextData
      });

      logger.info('Conversation context updated', {
        conversationId,
        hasAnalysisResults: !!analysisResults
      });

      return conversation;
    } catch (error) {
      logger.error('Error updating conversation context:', error);
      throw error;
    }
  }

  /**
   * Find existing assessment conversation
   */
  async findAssessmentConversation(userId, assessmentId) {
    try {
      const conversation = await Conversation.findOne({
        where: {
          user_id: userId,
          context_type: 'assessment',
          'context_data.assessment_id': assessmentId
        }
      });

      return conversation;
    } catch (error) {
      logger.error('Error finding assessment conversation:', error);
      return null;
    }
  }

  /**
   * Generate personalized welcome message
   */
  async generateAssessmentWelcomeMessage(conversationId, analysisResults) {
    try {
      const assessmentSummary = contextService.summarizeAssessmentData(analysisResults);
      
      if (!assessmentSummary) {
        return this.getDefaultWelcomeMessage();
      }

      const systemPrompt = `You are a career advisor AI. Generate a warm, personalized welcome message for a user who just completed their career assessment.

Assessment Summary: ${assessmentSummary}

Create a welcome message that:
1. Acknowledges their assessment completion
2. Highlights 2-3 key insights from their results
3. Expresses enthusiasm about helping with career guidance
4. Invites them to ask questions

Keep it conversational, encouraging, and under 150 words.`;

      const messages = [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: 'Generate my personalized welcome message based on my assessment results.' }
      ];

      const response = await openrouterService.generateResponse(messages, {
        maxTokens: this.welcomeMessageMaxTokens,
        temperature: 0.8
      });

      return response.content || this.getDefaultWelcomeMessage();
    } catch (error) {
      logger.error('Error generating assessment welcome message:', error);
      return this.getDefaultWelcomeMessage();
    }
  }

  /**
   * Generate assessment-based suggestions
   */
  async generateAssessmentSuggestions(analysisResults) {
    try {
      if (!analysisResults) {
        return this.getDefaultSuggestions();
      }

      const riasecTop = contextService.getTopRiasecTypes(analysisResults.riasec, 2);
      const oceanTop = contextService.getTopOceanTraits(analysisResults.ocean, 2);
      
      const suggestions = [];

      // RIASEC-based suggestions
      if (riasecTop.length > 0) {
        suggestions.push(`What career paths align with my ${riasecTop[0]} interests?`);
        if (riasecTop.length > 1) {
          suggestions.push(`How can I combine my ${riasecTop[0]} and ${riasecTop[1]} interests in one career?`);
        }
      }

      // Big Five-based suggestions
      if (oceanTop.length > 0) {
        suggestions.push(`How can I leverage my high ${oceanTop[0]} trait in my career?`);
      }

      // General suggestions
      suggestions.push('What are my strongest personality traits for leadership roles?');
      suggestions.push('Based on my assessment, what skills should I develop?');
      suggestions.push('What work environments would suit my personality best?');

      // Return top suggestions up to the configured count
      return suggestions.slice(0, this.suggestedQuestionsCount);
    } catch (error) {
      logger.error('Error generating assessment suggestions:', error);
      return this.getDefaultSuggestions();
    }
  }

  /**
   * Get default welcome message
   */
  getDefaultWelcomeMessage() {
    return `🎉 Congratulations on completing your career assessment! 

I'm here to help you explore your career options based on your unique personality profile and interests. Your assessment results provide valuable insights into your strengths, preferences, and potential career paths.

Feel free to ask me anything about:
- Career recommendations based on your results
- How your personality traits translate to work environments
- Skills you might want to develop
- Specific industries or roles that might interest you

What would you like to explore first?`;
  }

  /**
   * Get default suggestions
   */
  getDefaultSuggestions() {
    return [
      'What career paths match my personality and interests?',
      'How can I use my strengths in my career?',
      'What work environments would suit me best?',
      'What skills should I focus on developing?'
    ];
  }

  /**
   * Get handler status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      autoConversationEnabled: this.enableAutoConversation,
      personalizedWelcomeEnabled: this.enablePersonalizedWelcome,
      suggestedQuestionsEnabled: this.enableSuggestedQuestions,
      queueStatus: queueService.getStatus()
    };
  }
}

module.exports = new AssessmentEventHandler();
